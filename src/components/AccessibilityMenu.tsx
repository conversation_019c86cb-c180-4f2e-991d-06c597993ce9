import { View, Text, TouchableOpacity, Modal } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { 
  toggleDarkMode, 
  increaseLineHeight, 
  increaseLetterSpacing, 
  toggleTextAlign,
  toggleMenu
} from '../store/accessibilitySlice';

export default function AccessibilityMenu() {
  const dispatch = useDispatch();
  const { isMenuOpen, darkMode } = useSelector(state => state.accessibility);

  return (
    <Modal
      transparent={true}
      visible={isMenuOpen}
      animationType="slide"
      onRequestClose={() => dispatch(toggleMenu())}
    >
      <View className="flex-1 justify-end bg-black bg-opacity-50">
        <View className={`p-6 rounded-t-3xl ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <Text className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-black'}`}>
            Accessibility Menu
          </Text>
          
          <MenuItem 
            title="Dark/Light Mode" 
            onPress={() => dispatch(toggleDarkMode())}
            darkMode={darkMode}
          />
          <MenuItem 
            title="Increase Line Height" 
            onPress={() => dispatch(increaseLineHeight())}
            darkMode={darkMode}
          />
          <MenuItem 
            title="Increase Letter Spacing" 
            onPress={() => dispatch(increaseLetterSpacing())}
            darkMode={darkMode}
          />
          <MenuItem 
            title="Text Align" 
            onPress={() => dispatch(toggleTextAlign())}
            darkMode={darkMode}
          />
          
          <TouchableOpacity 
            onPress={() => dispatch(toggleMenu())}
            className={`mt-4 p-3 rounded-lg ${darkMode ? 'bg-blue-600' : 'bg-blue-500'}`}
          >
            <Text className="text-white text-center">Close</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

function MenuItem({ title, onPress, darkMode }) {
  return (
    <TouchableOpacity 
      onPress={onPress}
      className={`p-3 mb-2 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}
    >
      <Text className={darkMode ? 'text-white' : 'text-black'}>{title}</Text>
    </TouchableOpacity>
  );
}