import { View, Text, TouchableOpacity } from 'react-native';
import { useDispatch } from 'react-redux';
import { toggleMenu } from '../store/accessibilitySlice';

export default function Header() {
  const dispatch = useDispatch();

  return (
    <View className="flex-row justify-between items-center p-4 bg-blue-500">
      <Text className="text-white text-lg font-bold">LiftUP AI</Text>
      <View className="flex-row items-center">
        <Text className="text-white mr-2">English</Text>
        <TouchableOpacity 
          onPress={() => dispatch(toggleMenu())}
          className="bg-white p-2 rounded-full"
        >
          <Text className="text-blue-500">Accessibility</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}