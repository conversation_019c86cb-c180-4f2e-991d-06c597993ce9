import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  darkMode: false,
  lineHeight: 1.2,
  letterSpacing: 0,
  textAlign: 'left',
  isMenuOpen: false
};

export const accessibilitySlice = createSlice({
  name: 'accessibility',
  initialState,
  reducers: {
    toggleDarkMode: (state) => {
      state.darkMode = !state.darkMode;
    },
    increaseLineHeight: (state) => {
      state.lineHeight = state.lineHeight === 1.2 ? 1.5 : 1.8;
    },
    increaseLetterSpacing: (state) => {
      state.letterSpacing = state.letterSpacing === 0 ? 1 : 0;
    },
    toggleTextAlign: (state) => {
      state.textAlign = state.textAlign === 'left' ? 'center' : 'left';
    },
    toggleMenu: (state) => {
      state.isMenuOpen = !state.isMenuOpen;
    }
  }
});

export const { 
  toggleDarkMode, 
  increaseLineHeight, 
  increaseLetterSpacing, 
  toggleTextAlign,
  toggleMenu
} = accessibilitySlice.actions;

export default accessibilitySlice.reducer;