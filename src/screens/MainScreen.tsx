import { View, Text, TouchableOpacity } from 'react-native';
import { useSelector } from 'react-redux';
import Header from '../components/Header';
import AccessibilityMenu from '../components/AccessibilityMenu';

export default function MainScreen() {
  const { 
    darkMode, 
    lineHeight, 
    letterSpacing, 
    textAlign 
  } = useSelector(state => state.accessibility);

  const textStyle = {
    lineHeight: lineHeight * 16,
    letterSpacing: letterSpacing,
    textAlign: textAlign
  };

  return (
    <View className={`flex-1 ${darkMode ? 'bg-gray-900' : 'bg-white'}`}>
      <Header />
      
      <View className="flex-1 justify-center items-center p-6">
        <Text 
          className={`text-4xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-black'}`}
          style={textStyle}
        >
          LiftUP AI
        </Text>
        <Text 
          className={`text-2xl mb-8 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
          style={textStyle}
        >
          Welcome to
        </Text>
        <Text 
          className={`text-3xl font-bold mb-8 ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
          style={textStyle}
        >
          Your Smart Learning Companion!
        </Text>
        
        <View className="w-full">
          <TouchableOpacity 
            className={`p-4 rounded-lg mb-4 ${darkMode ? 'bg-blue-600' : 'bg-blue-500'}`}
          >
            <Text className="text-white text-center">Get Started</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-200'}`}
          >
            <Text className={darkMode ? 'text-white' : 'text-black'}>Log in</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      <AccessibilityMenu />
    </View>
  );
}